Unity Editor version:    6000.1.1f1 (7197418f847b)
Branch:                  6000.1/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.3.2 (Build 24D81)
Darwin version:          24.3.0
Architecture:            x86_64
Running under Rosetta:   NO
Available memory:        32768 MB
Date:                    2025-08-05T08:27:15Z
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-silverflute" at "2025-08-05T08:27:15.042322Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 4589, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              efcdddbee4e94b819befb6357a94e3b1
  Correlation Id:          857763e2c20671d56c2dd0179cfa67ef
  External correlation Id: 4358354089684680953
  Machine Id:              hzVApD/zBvVejW++rhjRfhQ/cyQ=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-silverflute" (connect: 0.00s, validation: 0.01s, handshake: 0.65s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-silverflute-notifications" at "2025-08-05T08:27:15.70268Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "1573678-UnityPersXXXX"
[Licensing::Client] Successfully updated license, isAync: True, time: 0.01
[Licensing::Client] Successfully resolved entitlement details
Pro License: NO
Launching external process: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/MacOS/Unity
-batchmode
-runTests
-testPlatform
EditMode
-testResults
TestResults_EditMode.xml
-logFile
TestLog_EditMode.txt
-projectPath
.
Successfully changed project path to: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
Fatal Error! It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
[Package Manager] Server process was shutdown
